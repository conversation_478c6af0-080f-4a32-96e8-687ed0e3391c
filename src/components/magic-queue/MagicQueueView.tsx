'use client'

import { useState, useEffect } from 'react'
import { MagicPromptInput } from './MagicPromptInput'
import { MagicQueueResults } from './MagicQueueResults'
import { aiService, MagicQueueResponse } from '@/lib/services/ai'
import { useDraftQueue } from '@/hooks/useDraftQueue'
import { DraftQueue } from '@/components/draft-queue/DraftQueue'
import { QueueCreationForm } from '@/components/draft-queue/QueueCreationForm'
import { useAuth } from '@/hooks/useAuth'

export function MagicQueueView() {
  const { user, isAuthenticated } = useAuth()
  const {
    draftItems,
    draftCount,
    isCreationMode,
    isEditMode,
    enterCreationMode,
    exitCreationMode
  } = useDraftQueue()

  // Magic queue state
  const [isGenerating, setIsGenerating] = useState(false)
  const [magicResults, setMagicResults] = useState<MagicQueueResponse | null>(null)
  const [error, setError] = useState<string | null>(null)
  const [isAIConfigured, setIsAIConfigured] = useState<boolean | null>(null)
  const [configCheckError, setConfigCheckError] = useState<string | null>(null)
  const [progressStep, setProgressStep] = useState<string>('')
  const [progressCurrent, setProgressCurrent] = useState<number>(0)
  const [progressTotal, setProgressTotal] = useState<number>(0)

  // Automatically enter creation mode when MagicQueueView becomes active (unless already in edit mode)
  useEffect(() => {
    if (!isCreationMode && !isEditMode) {
      enterCreationMode()
    }
  }, [isCreationMode, isEditMode, enterCreationMode])

  // Check AI configuration on mount (with caching)
  useEffect(() => {
    const checkAIConfig = async () => {
      try {
        // Check if we have a cached result from this session
        const cachedResult = sessionStorage.getItem('ai-config-check')
        if (cachedResult !== null) {
          const isConfigured = cachedResult === 'true'
          console.log('✅ Using cached AI configuration result:', isConfigured)
          setIsAIConfigured(isConfigured)
          if (!isConfigured) {
            setConfigCheckError('Firebase AI Logic is not properly configured. Please check your Firebase project settings.')
          }
          return
        }

        console.log('🔍 Starting AI configuration check...')
        setConfigCheckError(null)

        const isConfigured = await aiService.checkAIConfiguration()
        setIsAIConfigured(isConfigured)

        // Cache the result for this session
        sessionStorage.setItem('ai-config-check', isConfigured.toString())

        if (!isConfigured) {
          setConfigCheckError('Firebase AI Logic is not properly configured. Please check your Firebase project settings.')
        }
      } catch (error) {
        console.error('Failed to check AI configuration:', error)
        const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred'
        setConfigCheckError(`AI configuration check failed: ${errorMessage}`)
        setIsAIConfigured(false)

        // Cache the failed result for this session
        sessionStorage.setItem('ai-config-check', 'false')
      }
    }

    checkAIConfig()
  }, [])

  const handleGenerateMagicQueue = async (prompt: string, count: number, maxDuration?: number) => {
    if (!prompt.trim()) return

    setIsGenerating(true)
    setError(null)
    setMagicResults(null)
    setProgressStep('Starting...')
    setProgressCurrent(0)
    setProgressTotal(3)

    try {
      console.log('🪄 Generating magic queue for prompt:', prompt)
      console.log('📊 Parameters:', { count, maxDuration })

      const response = await aiService.generateVideoRecommendations({
        prompt: prompt.trim(),
        count,
        maxDuration,
        onProgress: (step: string, current: number, total: number) => {
          setProgressStep(step)
          setProgressCurrent(current)
          setProgressTotal(total)
        }
      })

      setMagicResults(response)
      console.log('✅ Magic queue generated successfully:', response)
    } catch (error) {
      console.error('❌ Failed to generate magic queue:', error)
      setError(error instanceof Error ? error.message : 'Failed to generate magic queue. Please try again.')
    } finally {
      setIsGenerating(false)
      setProgressStep('')
      setProgressCurrent(0)
      setProgressTotal(0)
    }
  }

  // Show AI configuration error
  if (isAIConfigured === false) {
    return (
      <div className="space-y-6">
        <div className="glassmorphism rounded-2xl p-6">
          <div className="text-center">
            <div className="w-16 h-16 bg-red-500/20 rounded-full flex items-center justify-center mx-auto mb-4">
              <svg width="32" height="32" viewBox="0 0 24 24" fill="currentColor" className="text-red-400">
                <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
              </svg>
            </div>
            <h2 className="text-xl font-bold text-white mb-2">Firebase AI Logic Not Configured</h2>
            <p className="text-dark-300 mb-4">
              To use Magic Queue, you need to configure Firebase AI Logic in your project.
            </p>
            <div className="text-left bg-dark-800/50 rounded-lg p-4 mb-4">
              <h3 className="text-sm font-semibold text-white mb-2">Setup Instructions:</h3>
              <ol className="text-sm text-dark-300 space-y-1 list-decimal list-inside">
                <li>Go to the Firebase Console</li>
                <li>Navigate to the Firebase AI Logic section</li>
                <li>Enable the Gemini API (Developer or Vertex AI)</li>
                <li>Configure your API keys and billing if required</li>
                <li>Refresh this page</li>
              </ol>
            </div>
            <a
              href="https://console.firebase.google.com/project/_/ailogic"
              target="_blank"
              rel="noopener noreferrer"
              className="btn-primary inline-flex items-center space-x-2"
            >
              <span>Open Firebase Console</span>
              <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                <path d="M14,3V5H17.59L7.76,14.83L9.17,16.24L19,6.41V10H21V3M19,19H5V5H12V3H5C3.89,3 3,3.9 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19V12H19V19Z"/>
              </svg>
            </a>
          </div>
        </div>
      </div>
    )
  }

  // Show loading while checking configuration
  if (isAIConfigured === null) {
    return (
      <div className="glassmorphism rounded-2xl p-6">
        <div className="flex flex-col items-center justify-center space-y-4 py-8">
          <div className="w-8 h-8 border-2 border-primary-400/30 border-t-primary-400 rounded-full animate-spin"></div>
          <div className="text-center">
            <h3 className="text-lg font-semibold text-white mb-2">Checking AI Configuration</h3>
            <p className="text-dark-300">Verifying Firebase AI Logic setup...</p>
            {configCheckError && (
              <div className="mt-4 p-4 bg-red-500/10 border border-red-500/20 rounded-lg">
                <p className="text-red-400 text-sm">{configCheckError}</p>
              </div>
            )}
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Queue Creation Mode Header */}
      {isCreationMode && (
        <div className="glassmorphism rounded-2xl p-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="w-10 h-10 bg-gradient-to-br from-primary-500 to-purple-600 rounded-full flex items-center justify-center text-white font-bold text-lg">
                <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M7.5 5.6L10 7L8.6 4.5L10 2L7.5 3.4L5 2L6.4 4.5L5 7L7.5 5.6ZM19.5 15.4L22 14L20.6 16.5L22 19L19.5 17.6L17 19L18.4 16.5L17 14L19.5 15.4ZM22 2L20.6 4.5L22 7L19.5 5.6L17 7L18.4 4.5L17 2L19.5 3.4L22 2Z"/>
                </svg>
              </div>
              <div>
                <h3 className="text-lg font-semibold text-white">Creating Magic Queue</h3>
                <p className="text-dark-300">Let AI curate the perfect video queue for you</p>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Magic Prompt Input */}
      <div className="glassmorphism rounded-2xl p-6">
        <div className="text-center mb-6">
          <h1 className="text-2xl font-bold text-white mb-2">
            {isEditMode ? 'Edit Queue - Magic Recommendations' : 'Magic Queue Generator'}
          </h1>
          <p className="text-dark-300">
            {isEditMode 
              ? 'Get AI-powered video recommendations to enhance your queue' 
              : 'Describe what you want to watch and let AI create the perfect queue for you'
            }
          </p>
        </div>

        <MagicPromptInput
          onSubmit={handleGenerateMagicQueue}
          isLoading={isGenerating}
          placeholder="Describe what kind of videos you'd like to watch..."
        />
      </div>

      {/* Error Display */}
      {error && (
        <div className="glassmorphism rounded-2xl p-6 border border-red-500/20">
          <div className="flex items-start space-x-3">
            <div className="w-6 h-6 text-red-400 flex-shrink-0">
              <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
              </svg>
            </div>
            <div>
              <h3 className="text-red-400 font-semibold mb-1">Error</h3>
              <p className="text-dark-300">{error}</p>
            </div>
          </div>
        </div>
      )}

      {/* Magic Queue Results */}
      {(magicResults || isGenerating) && (
        <MagicQueueResults
          videos={magicResults?.videos || []}
          explanation={magicResults?.explanation}
          searchQueries={magicResults?.searchQueries}
          isLoading={isGenerating}
          progressStep={progressStep}
          progressCurrent={progressCurrent}
          progressTotal={progressTotal}
        />
      )}

      {/* Draft Queue Display (show when in creation or edit mode) */}
      {(isCreationMode || isEditMode) && <DraftQueue />}

      {/* Queue Creation/Edit Form (show when in creation or edit mode) */}
      {(isCreationMode || isEditMode) && (
        <QueueCreationForm />
      )}
    </div>
  )
}
